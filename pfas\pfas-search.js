// Variables globales
let searchInProgress = false;
let resultsFound = false;

// Éléments DOM
const startButton = document.getElementById('start-pfas-search');
const loadingDiv = document.getElementById('pfas-loading');
const progressBar = document.getElementById('pfas-progress');
const statusText = document.getElementById('pfas-status');
const resultsDiv = document.getElementById('pfas-results');
const noResultsDiv = document.getElementById('pfas-no-results');

// Event listener pour le bouton de démarrage
startButton.addEventListener('click', startPFASSearch);

// Fonction principale pour démarrer la recherche PFAS
async function startPFASSearch() {
    if (searchInProgress) return;
    
    searchInProgress = true;
    resultsFound = false;
    
    // Réinitialiser l'interface
    resetInterface();
    
    // Afficher la barre de progression
    showLoading();
    
    try {
        await searchPFASData();
    } catch (error) {
        console.error('Erreur lors de la recherche PFAS:', error);
        showError('Une erreur est survenue lors de la recherche. Veuillez réessayer.');
    } finally {
        searchInProgress = false;
        hideLoading();
        
        if (!resultsFound) {
            showNoResults();
        }
    }
}

// Fonction pour rechercher les données PFAS
async function searchPFASData() {
    const totalParams = PFAS_PARAMS.length;
    let currentParam = 0;
    
    for (const param of PFAS_PARAMS) {
        currentParam++;
        
        // Mettre à jour la progression
        updateProgress(currentParam, totalParams, `Recherche ${param.name}...`);
        
        try {
            const apiUrl = `/get-qualite-eau.php?code_commune=${COMMUNE_CODE}&code_parametre=${param.code}`;
            
            const response = await fetch(apiUrl);
            if (!response.ok) {
                throw new Error(`Erreur HTTP: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.data && data.data.length > 0) {
                displayPFASResult(data.data[0], param);
                resultsFound = true;
            }
            
            // Délai entre les requêtes pour éviter de surcharger l'API
            if (currentParam < totalParams) {
                await delay(1500);
            }
            
        } catch (error) {
            console.error(`Erreur pour le paramètre ${param.name}:`, error);
            displayPFASError(param, error.message);
        }
    }
}

// Fonction pour afficher un résultat PFAS
function displayPFASResult(data, param) {
    const resultItem = document.createElement('div');
    resultItem.classList.add('pfas-result-item');
    
    // Déterminer le statut de conformité
    const isConform = determineConformity(data);
    const conformityClass = isConform ? 'conform' : 'non-conform';
    
    // Formater la date
    const date = new Date(data.date_prelevement).toLocaleDateString('fr-FR');
    
    // Créer le contenu HTML
    resultItem.innerHTML = `
        <div class="pfas-result-header ${conformityClass}">
            <h3>${param.name} - ${param.fullName}</h3>
            <span class="conformity-badge ${conformityClass}">
                ${isConform ? 'Conforme' : 'Non conforme'}
            </span>
        </div>
        <div class="pfas-result-content">
            <div class="result-main-info">
                <p class="result-value">
                    <strong>Résultat :</strong> 
                    ${data.resultat_numerique || 'Non détecté'}
                    ${data.libelle_unite && data.libelle_unite !== "SANS OBJET" ? ` ${data.libelle_unite}` : ''}
                </p>
                <p class="result-date">
                    <strong>Date du prélèvement :</strong> ${date}
                </p>
            </div>
            <div class="result-details">
                <div class="detail-item">
                    <span class="detail-label">Limite de qualité :</span>
                    <span class="detail-value">${data.limite_qualite_parametre || 'Non spécifiée'}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Référence de qualité :</span>
                    <span class="detail-value">${data.reference_qualite_parametre || 'Non spécifiée'}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Conclusion :</span>
                    <span class="detail-value">${data.conclusion_conformite_prelevement || 'Non spécifiée'}</span>
                </div>
            </div>
            <div class="result-provider-info">
                <div class="provider-item">
                    <span class="provider-label">Distributeur :</span>
                    <span class="provider-value">${data.nom_distributeur || 'Non spécifié'}</span>
                </div>
                <div class="provider-item">
                    <span class="provider-label">Unité de gestion :</span>
                    <span class="provider-value">${data.nom_uge || 'Non spécifiée'}</span>
                </div>
                <div class="provider-item">
                    <span class="provider-label">Maître d'ouvrage :</span>
                    <span class="provider-value">${data.nom_moa || 'Non spécifié'}</span>
                </div>
            </div>
        </div>
    `;
    
    resultsDiv.appendChild(resultItem);
}

// Fonction pour afficher une erreur pour un paramètre spécifique
function displayPFASError(param, errorMessage) {
    const errorItem = document.createElement('div');
    errorItem.classList.add('pfas-error-item');
    
    errorItem.innerHTML = `
        <div class="pfas-error-header">
            <h3>${param.name} - ${param.fullName}</h3>
            <span class="error-badge">Erreur</span>
        </div>
        <div class="pfas-error-content">
            <p>Impossible de récupérer les données pour ce paramètre.</p>
            <p class="error-details">${errorMessage}</p>
        </div>
    `;
    
    resultsDiv.appendChild(errorItem);
}

// Fonction pour déterminer la conformité
function determineConformity(data) {
    return data.conformite_references_pc_prelevement === "C" &&
           data.conformite_references_bact_prelevement === "C" &&
           data.conformite_limites_pc_prelevement === "C" &&
           data.conformite_limites_bact_prelevement === "C";
}

// Fonctions utilitaires pour l'interface
function resetInterface() {
    resultsDiv.innerHTML = '';
    noResultsDiv.style.display = 'none';
    startButton.disabled = true;
    startButton.textContent = 'Recherche en cours...';
}

function showLoading() {
    loadingDiv.style.display = 'block';
    updateProgress(0, PFAS_PARAMS.length, 'Initialisation...');
}

function hideLoading() {
    loadingDiv.style.display = 'none';
    startButton.disabled = false;
    startButton.textContent = 'Relancer l\'analyse PFAS';
}

function updateProgress(current, total, status) {
    const percentage = (current / total) * 100;
    progressBar.style.width = `${percentage}%`;
    statusText.textContent = status;
}

function showNoResults() {
    noResultsDiv.style.display = 'block';
}

function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.classList.add('error-message');
    errorDiv.innerHTML = `
        <h3>Erreur</h3>
        <p>${message}</p>
    `;
    resultsDiv.appendChild(errorDiv);
}

// Fonction utilitaire pour créer un délai
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    console.log(`Page PFAS initialisée pour ${VILLE_NOM} (${COMMUNE_CODE})`);
    console.log(`${PFAS_PARAMS.length} paramètres PFAS à analyser`);
});
