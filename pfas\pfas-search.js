// Variables globales
let searchInProgress = false;
let resultsFound = false;
let resultsCount = 0;

// Éléments DOM
const startButton = document.getElementById('start-pfas-search');
const loadingDiv = document.getElementById('pfas-loading');
const progressBar = document.getElementById('pfas-progress');
const statusText = document.getElementById('pfas-status');
const resultsDiv = document.getElementById('pfas-results');
const noResultsDiv = document.getElementById('pfas-no-results');

// Event listener pour le bouton de démarrage
startButton.addEventListener('click', startPFASSearch);

// Fonction principale pour démarrer la recherche PFAS
async function startPFASSearch() {
    if (searchInProgress) return;

    searchInProgress = true;
    resultsFound = false;

    // Réinitialiser l'interface
    resetInterface();

    // Afficher la barre de progression
    showLoading();

    try {
        await searchPFASData();
    } catch (error) {
        console.error('Erreur lors de la recherche PFAS:', error);
        showError('Une erreur est survenue lors de la recherche. Veuillez réessayer.');
    } finally {
        searchInProgress = false;
        hideLoading();

        if (!resultsFound) {
            showNoResults();
        }
    }
}

// Fonction pour rechercher les données PFAS
async function searchPFASData() {
    const totalParams = PFAS_PARAMS.length;
    let currentParam = 0;

    for (const param of PFAS_PARAMS) {
        currentParam++;

        // Mettre à jour la progression
        updateProgress(currentParam, totalParams, `Recherche ${param.name}...`);

        try {
            const apiUrl = `/get-qualite-eau.php?code_commune=${COMMUNE_CODE}&code_parametre=${param.code}`;

            const response = await fetch(apiUrl);
            if (!response.ok) {
                throw new Error(`Erreur HTTP: ${response.status}`);
            }

            const data = await response.json();

            if (data.data && data.data.length > 0) {
                // Afficher le résultat immédiatement
                displayPFASResult(data.data[0], param);
                resultsFound = true;
                resultsCount++;
                updateResultsCounter();
            } else {
                // Afficher un message "pas de données" pour ce paramètre
                displayPFASNoData(param);
            }

            // Pause de 2 secondes entre chaque requête (sauf pour la dernière)
            if (currentParam < totalParams) {
                await delay(2000);
            }

        } catch (error) {
            console.error(`Erreur pour le paramètre ${param.name}:`, error);
            displayPFASError(param, error.message);

            // Pause même en cas d'erreur
            if (currentParam < totalParams) {
                await delay(2000);
            }
        }
    }
}

// Fonction pour afficher un résultat PFAS
function displayPFASResult(data, param) {
    const resultItem = document.createElement('div');
    resultItem.classList.add('pfas-result-item');

    // Déterminer le statut de conformité
    const isConform = determineConformity(data);
    const conformityClass = isConform ? 'conform' : 'non-conform';

    // Formater la date
    const date = new Date(data.date_prelevement).toLocaleDateString('fr-FR');

    // Traitement spécial pour la somme des PFAS
    const isSum = param.code === "8847";
    const headerClass = isSum ? `${conformityClass} sum-pfas` : conformityClass;

    // Champs à ignorer lors de l'affichage
    const fieldsToIgnore = [
        'code_departement', 'nom_departement', 'code_parametre', 'code_parametre_cas',
        'code_parametre_se', 'libelle_parametre_maj', 'libelle_parametre_web',
        'code_type_parametre', 'code_lieu_analyse', 'code_commune', 'nom_commune'
    ];

    // Organiser les détails par sections
    const sections = {
        'Résultats de l\'analyse': ['libelle_parametre', 'resultat_numerique', 'resultat_alphanumerique', 'libelle_unite', 'limite_detection', 'limite_quantification', 'incertitude_mesure'],
        'Conformité et qualité': ['limite_qualite_parametre', 'reference_qualite_parametre', 'conclusion_conformite_prelevement', 'conformite_limites_bact_prelevement', 'conformite_limites_pc_prelevement', 'conformite_references_bact_prelevement', 'conformite_references_pc_prelevement'],
        'Prélèvement': ['date_prelevement', 'heure_prelevement', 'code_prelevement', 'nom_prelevement', 'preleveur', 'temperature_echantillon'],
        'Analyse': ['date_analyse', 'code_analyse', 'laboratoire', 'methode_analyse'],
        'Réseau et distribution': ['code_reseau_distribution', 'nom_reseau_distribution', 'nom_distributeur', 'nom_uge', 'nom_moa'],
        'Installations': ['code_installation_amont', 'nom_installation_amont', 'code_installation_aval', 'nom_installation_aval']
    };

    let allDetailsHTML = '';

    // Afficher les sections organisées
    for (const [sectionTitle, fields] of Object.entries(sections)) {
        let sectionHTML = '';
        let hasData = false;

        for (const field of fields) {
            if (data[field] !== null && data[field] !== '' && data[field] !== undefined) {
                const label = formatFieldLabel(field);
                const formattedValue = formatFieldValue(field, data[field]);
                sectionHTML += `
                    <div class="detail-item">
                        <span class="detail-label">${label} :</span>
                        <span class="detail-value">${formattedValue}</span>
                    </div>
                `;
                hasData = true;
            }
        }

        if (hasData) {
            allDetailsHTML += `
                <div class="detail-section">
                    <h5 class="section-title">${sectionTitle}</h5>
                    ${sectionHTML}
                </div>
            `;
        }
    }

    // Ajouter les champs non catégorisés
    let otherFieldsHTML = '';
    for (const [key, value] of Object.entries(data)) {
        if (!fieldsToIgnore.includes(key) && value !== null && value !== '' &&
            !Object.values(sections).flat().includes(key)) {
            const label = formatFieldLabel(key);
            const formattedValue = formatFieldValue(key, value);
            otherFieldsHTML += `
                <div class="detail-item">
                    <span class="detail-label">${label} :</span>
                    <span class="detail-value">${formattedValue}</span>
                </div>
            `;
        }
    }

    if (otherFieldsHTML) {
        allDetailsHTML += `
            <div class="detail-section">
                <h5 class="section-title">Autres informations</h5>
                ${otherFieldsHTML}
            </div>
        `;
    }

    // Créer le contenu HTML
    resultItem.innerHTML = `
        <div class="pfas-result-header ${headerClass}">
            <h3>${param.name} - ${param.fullName}</h3>
            <span class="conformity-badge ${conformityClass}">
                ${isConform ? 'Conforme' : 'Non conforme'}
            </span>
        </div>
        <div class="pfas-result-content">
            <div class="result-main-info">
                <p class="result-value">
                    <strong>Résultat :</strong>
                    ${data.resultat_numerique || data.resultat_alphanumerique || 'Non détecté'}
                    ${data.libelle_unite && data.libelle_unite !== "SANS OBJET" ? ` ${data.libelle_unite}` : ''}
                </p>
                <p class="result-date">
                    <strong>Date du prélèvement :</strong> ${date}
                </p>
            </div>
            <div class="result-details-complete">
                <h4>Détails complets de l'analyse</h4>
                <div class="all-details">
                    ${allDetailsHTML}
                </div>
            </div>
        </div>
    `;

    resultsDiv.appendChild(resultItem);

    // Faire défiler vers le nouveau résultat
    resultItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

// Fonction pour formater les labels des champs
function formatFieldLabel(fieldName) {
    const labelMap = {
        'libelle_parametre': 'Paramètre',
        'resultat_numerique': 'Résultat numérique',
        'resultat_alphanumerique': 'Résultat alphanumérique',
        'libelle_unite': 'Unité',
        'limite_qualite_parametre': 'Limite de qualité',
        'reference_qualite_parametre': 'Référence de qualité',
        'nom_uge': 'Unité de gestion',
        'nom_distributeur': 'Distributeur',
        'nom_moa': 'Maître d\'ouvrage',
        'date_prelevement': 'Date de prélèvement',
        'conclusion_conformite_prelevement': 'Conclusion conformité',
        'conformite_limites_bact_prelevement': 'Conformité limites bactériologiques',
        'conformite_limites_pc_prelevement': 'Conformité limites physico-chimiques',
        'conformite_references_bact_prelevement': 'Conformité références bactériologiques',
        'conformite_references_pc_prelevement': 'Conformité références physico-chimiques',
        'code_reseau_distribution': 'Code réseau distribution',
        'nom_reseau_distribution': 'Nom réseau distribution',
        'code_installation_amont': 'Code installation amont',
        'nom_installation_amont': 'Nom installation amont',
        'code_installation_aval': 'Code installation aval',
        'nom_installation_aval': 'Nom installation aval',
        'code_prelevement': 'Code prélèvement',
        'nom_prelevement': 'Nom prélèvement',
        'code_analyse': 'Code analyse',
        'date_analyse': 'Date analyse',
        'heure_prelevement': 'Heure prélèvement',
        'temperature_echantillon': 'Température échantillon',
        'preleveur': 'Préleveur',
        'laboratoire': 'Laboratoire',
        'methode_analyse': 'Méthode d\'analyse',
        'incertitude_mesure': 'Incertitude de mesure',
        'limite_detection': 'Limite de détection',
        'limite_quantification': 'Limite de quantification'
    };

    return labelMap[fieldName] || fieldName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

// Fonction pour formater les valeurs des champs
function formatFieldValue(fieldName, value) {
    if (fieldName.includes('date') && value) {
        return new Date(value).toLocaleDateString('fr-FR');
    }

    if (fieldName === 'heure_prelevement' && value) {
        return value;
    }

    if (fieldName.includes('conformite_') && value) {
        const conformityMap = {
            'C': 'Conforme',
            'N': 'Non conforme',
            'D': 'Dérogation',
            'S': 'Sans objet'
        };
        return conformityMap[value] || value;
    }

    return value || 'Non spécifié';
}

// Fonction pour afficher un message "pas de données"
function displayPFASNoData(param) {
    const noDataItem = document.createElement('div');
    noDataItem.classList.add('pfas-no-data-item');

    // Traitement spécial pour la somme des PFAS
    const isSum = param.code === "8847";
    const headerClass = isSum ? 'no-data sum-pfas' : 'no-data';

    noDataItem.innerHTML = `
        <div class="pfas-result-header ${headerClass}">
            <h3>${param.name} - ${param.fullName}</h3>
            <span class="no-data-badge">Pas de données</span>
        </div>
        <div class="pfas-result-content">
            <div class="no-data-message">
                <p>Aucune donnée disponible pour ce paramètre dans cette commune.</p>
                <p class="no-data-explanation">
                    Cela peut signifier que ce paramètre n'a pas encore été analysé ou que les résultats ne sont pas disponibles dans la base de données publique.
                </p>
            </div>
        </div>
    `;

    resultsDiv.appendChild(noDataItem);

    // Faire défiler vers le nouveau résultat
    noDataItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

// Fonction pour afficher une erreur pour un paramètre spécifique
function displayPFASError(param, errorMessage) {
    const errorItem = document.createElement('div');
    errorItem.classList.add('pfas-error-item');

    errorItem.innerHTML = `
        <div class="pfas-error-header">
            <h3>${param.name} - ${param.fullName}</h3>
            <span class="error-badge">Erreur</span>
        </div>
        <div class="pfas-error-content">
            <p>Impossible de récupérer les données pour ce paramètre.</p>
            <p class="error-details">${errorMessage}</p>
        </div>
    `;

    resultsDiv.appendChild(errorItem);
}

// Fonction pour déterminer la conformité
function determineConformity(data) {
    return data.conformite_references_pc_prelevement === "C" &&
           data.conformite_references_bact_prelevement === "C" &&
           data.conformite_limites_pc_prelevement === "C" &&
           data.conformite_limites_bact_prelevement === "C";
}

// Fonctions utilitaires pour l'interface
function resetInterface() {
    resultsDiv.innerHTML = '';
    noResultsDiv.style.display = 'none';
    startButton.disabled = true;
    startButton.textContent = 'Recherche en cours...';
    resultsCount = 0;
    updateResultsCounter();
}

function showLoading() {
    loadingDiv.style.display = 'block';
    document.getElementById('results-counter-container').style.display = 'block';
    updateProgress(0, PFAS_PARAMS.length, 'Initialisation...');
}

function hideLoading() {
    loadingDiv.style.display = 'none';
    startButton.disabled = false;
    startButton.textContent = 'Relancer l\'analyse PFAS';
}

function updateProgress(current, total, status) {
    const percentage = (current / total) * 100;
    progressBar.style.width = `${percentage}%`;
    statusText.textContent = status;
}

function showNoResults() {
    noResultsDiv.style.display = 'block';
}

function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.classList.add('error-message');
    errorDiv.innerHTML = `
        <h3>Erreur</h3>
        <p>${message}</p>
    `;
    resultsDiv.appendChild(errorDiv);
}

// Fonction utilitaire pour créer un délai
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Fonction pour mettre à jour le compteur de résultats
function updateResultsCounter() {
    const counterElement = document.getElementById('results-counter');
    if (counterElement) {
        counterElement.textContent = `${resultsCount} résultat(s) trouvé(s)`;
    }
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    console.log(`Page PFAS initialisée pour ${VILLE_NOM} (${COMMUNE_CODE})`);
    console.log(`${PFAS_PARAMS.length} paramètres PFAS à analyser`);
});
