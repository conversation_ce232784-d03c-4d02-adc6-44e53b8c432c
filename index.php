<?php

$request_uri = $_SERVER['REQUEST_URI'];

//echo "<br> request_uri : " . $request_uri;

// Extraire le chemin de l'URL
$path = parse_url($request_uri, PHP_URL_PATH);

//echo "<br> path : " . $path;

// Diviser le chemin en segments
$segments = explode('/', trim($path, '/'));

if ($segments[0] === 'analyse') {

    // Inclure le fichier de template pour afficher la page
    include('analyse/template.php');

} 
elseif ($segments[0] === 'villes') {
    include('villes.php') ;
}
elseif ($segments[0] === 'contact') {
    include('contact.php') ;
}

elseif ($segments[0] === 'mentions-legales') {
    include('mentions-legales.php') ;
}

elseif ($segments[0] === 'verifier') {
    include('verifier.php') ;
}

else {
    // Sinon, gérer la route dynamique
    include('home.php');
}
?>

