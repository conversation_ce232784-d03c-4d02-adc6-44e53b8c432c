# Module PFAS - Analyse des substances per- et polyfluoroalkylées

## Description

Ce module permet d'analyser la présence de PFAS (substances per- et polyfluoroalkylées) dans l'eau potable des communes françaises. Il utilise l'API Hub'Eau pour récupérer les données officielles du contrôle sanitaire.

## Fonctionnalités

- **Recherche asynchrone** : Analyse en temps réel avec barre de progression
- **8 paramètres PFAS** : Analyse des principaux PFAS surveillés
- **Interface responsive** : Compatible mobile et desktop
- **Données officielles** : Utilise l'API Hub'Eau du gouvernement français

## Structure des fichiers

```
pfas/
├── template.php        # Template principal pour les pages PFAS
├── index.php          # Page d'accueil du module PFAS
├── pfas-search.js     # JavaScript pour la recherche asynchrone
├── style-pfas.css     # Styles spécifiques au module PFAS
└── README.md          # Documentation
```

## Paramètres PFAS analysés

| Code | Nom | Description |
|------|-----|-------------|
| 7102 | PFOA | Acide perfluorooctanoïque |
| 7103 | PFOS | Acide perfluorooctane sulfonique |
| 7104 | PFNA | Acide perfluorononanoïque |
| 7105 | PFDA | Acide perfluorodécanoïque |
| 7106 | PFHxS | Acide perfluorohexane sulfonique |
| 7107 | PFBS | Acide perfluorobutane sulfonique |
| 7108 | PFHpA | Acide perfluoroheptanoïque |
| 7109 | PFBA | Acide perfluorobutanoïque |

## Utilisation

### URL de base
```
/pfas/[ville]-[code_commune]/
```

### Exemples d'URLs
- `/pfas/lyon-69123/` - Analyse PFAS pour Lyon
- `/pfas/paris-75056/` - Analyse PFAS pour Paris
- `/pfas/marseille-13055/` - Analyse PFAS pour Marseille

### Processus de recherche

1. L'utilisateur visite une URL PFAS
2. La page se charge avec les informations de la commune
3. L'utilisateur clique sur "Lancer l'analyse PFAS"
4. Le JavaScript lance une recherche asynchrone pour chaque paramètre PFAS
5. Les résultats s'affichent au fur et à mesure
6. Une barre de progression indique l'avancement

## API utilisée

Le module utilise l'endpoint existant `get-qualite-eau.php` qui fait appel à l'API Hub'Eau :

```
https://hubeau.eaufrance.fr/api/v1/qualite_eau_potable/resultats_dis
```

### Paramètres de l'API
- `code_commune` : Code INSEE de la commune
- `code_parametre` : Code du paramètre PFAS à analyser
- `fields` : Champs à récupérer
- `size` : Nombre de résultats (limité à 1 pour le dernier résultat)

## Fonctionnalités techniques

### Recherche asynchrone
- Délai de 1,5 seconde entre chaque requête API
- Gestion des erreurs pour chaque paramètre
- Barre de progression en temps réel
- Affichage des résultats au fur et à mesure

### Interface utilisateur
- Design responsive
- Badges de conformité colorés
- Informations détaillées sur chaque paramètre
- Gestion des cas sans données

### Gestion des erreurs
- Affichage des erreurs par paramètre
- Message global si aucune donnée n'est trouvée
- Gestion des timeouts API

## Installation

1. Ajouter la route dans `index.php` :
```php
elseif ($segments[0] === 'pfas') {
    include('pfas/template.php') ;
}
```

2. Créer le dossier `pfas/` avec tous les fichiers
3. Ajouter le lien dans la navigation (optionnel)

## Configuration

### Base de données
Le module utilise la même configuration de base de données que le reste de l'application pour récupérer les informations des communes.

### Codes PFAS
Les codes des paramètres PFAS sont définis dans `pfas-search.js` dans la constante `PFAS_PARAMS`.

## Personnalisation

### Ajouter de nouveaux paramètres PFAS
Modifier la constante `PFAS_PARAMS` dans `pfas-search.js` :

```javascript
const PFAS_PARAMS = [
    { code: "7102", name: "PFOA", fullName: "Acide perfluorooctanoïque" },
    // Ajouter de nouveaux paramètres ici
];
```

### Modifier les styles
Éditer `style-pfas.css` pour personnaliser l'apparence.

### Changer les délais API
Modifier la valeur dans `pfas-search.js` :

```javascript
await delay(1500); // Délai en millisecondes
```

## Compatibilité

- **PHP** : 7.4+
- **Navigateurs** : Chrome, Firefox, Safari, Edge (versions récentes)
- **API** : Hub'Eau v1

## Limitations

- Dépend de la disponibilité de l'API Hub'Eau
- Les données PFAS ne sont pas disponibles pour toutes les communes
- Limite de 1 résultat par paramètre (le plus récent)

## Support

Pour toute question ou problème, consulter :
- Documentation API Hub'Eau : https://hubeau.eaufrance.fr/
- Code source du projet principal
