<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Vérification des paramètres
if (!isset($_GET['code_commune'])) {
    echo json_encode(['error' => 'Code commune manquant']);
    exit;
}

$communeCode = $_GET['code_commune'];

// Codes des paramètres PFAS
$pfasParams = [
    '7102' => 'PFOA',
    '7103' => 'PFOS', 
    '7104' => 'PFNA',
    '7105' => 'PFDA',
    '7106' => 'PFHxS',
    '7107' => 'PFBS',
    '7108' => 'PFHpA',
    '7109' => 'PFBA'
];

$results = [];
$errors = [];

// Récupération des données pour chaque paramètre PFAS
foreach ($pfasParams as $paramCode => $paramName) {
    try {
        $apiUrl = "https://hubeau.eaufrance.fr/api/v1/qualite_eau_potable/resultats_dis?code_commune={$communeCode}&code_parametre={$paramCode}&fields=libelle_parametre,resultat_numerique,libelle_unite,limite_qualite_parametre,reference_qualite_parametre,nom_uge,nom_distributeur,nom_moa,date_prelevement,conclusion_conformite_prelevement,conformite_limites_bact_prelevement,conformite_limites_pc_prelevement,conformite_references_bact_prelevement,conformite_references_pc_prelevement&size=1";
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'user_agent' => 'LeauPotable.fr PFAS Checker'
            ]
        ]);
        
        $response = file_get_contents($apiUrl, false, $context);
        
        if ($response === FALSE) {
            $errors[$paramCode] = "Impossible de récupérer les données pour {$paramName}";
            continue;
        }
        
        $data = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            $errors[$paramCode] = "Erreur de décodage JSON pour {$paramName}";
            continue;
        }
        
        if (isset($data['data']) && !empty($data['data'])) {
            $results[$paramCode] = [
                'param_name' => $paramName,
                'param_code' => $paramCode,
                'data' => $data['data'][0]
            ];
        } else {
            $results[$paramCode] = [
                'param_name' => $paramName,
                'param_code' => $paramCode,
                'data' => null,
                'message' => 'Aucune donnée disponible'
            ];
        }
        
        // Délai pour éviter de surcharger l'API
        usleep(500000); // 0.5 seconde
        
    } catch (Exception $e) {
        $errors[$paramCode] = "Erreur pour {$paramName}: " . $e->getMessage();
    }
}

// Préparation de la réponse
$response = [
    'commune_code' => $communeCode,
    'total_params' => count($pfasParams),
    'results_found' => count(array_filter($results, function($result) {
        return $result['data'] !== null;
    })),
    'results' => $results,
    'errors' => $errors,
    'timestamp' => date('Y-m-d H:i:s')
];

echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
